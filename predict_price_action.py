"""
🔮 QUOTEX AI PRICE ACTION PREDICTOR
==================================

Uses trained model to predict next price movement based on current market data.
"""

import os
import json
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

try:
    import tensorflow as tf
    import pickle
    from sklearn.preprocessing import MinMaxScaler
    print("✅ ML libraries loaded")
except ImportError as e:
    print(f"❌ Missing ML libraries: {e}")
    exit(1)

try:
    from quotexapi.stable_api import Quotex
    from quotexapi.config import email, password
    print("✅ Quotex API loaded")
except ImportError as e:
    print(f"❌ Quotex API not found: {e}")
    exit(1)

# ============================================================================
# 🔧 CONFIGURATION
# ============================================================================

CONFIG = {
    "email": email,
    "password": password,
    "asset": "EURUSD_otc",
    "period": 60,
    "model_path": "models/brain_latest.h5",
    "metadata_path": "models/price_action_model_EURUSD_otc_60s_metadata.pkl",
    "sequence_length": 60,
    "confidence_threshold": 0.7
}

# ============================================================================
# 🔮 PRICE ACTION PREDICTOR
# ============================================================================

class PriceActionPredictor:

    def __init__(self):
        self.client = None
        self.model = None
        self.scaler = None
        self.feature_columns = None
        self.sequence_length = CONFIG["sequence_length"]

    async def connect(self, max_retries=5):
        """Connect to Quotex API with enhanced error handling"""
        print("🔌 Connecting to Quotex...")

        for attempt in range(max_retries):
            try:
                print(f"Attempt {attempt + 1}/{max_retries}")

                # Clean up any existing client
                if self.client:
                    try:
                        self.client.close()
                    except:
                        pass

                # Create new client with different settings for each attempt
                self.client = Quotex(
                    email=CONFIG["email"],
                    password=CONFIG["password"],
                    lang="en",
                    user_data_dir=f"browser_temp_{attempt}"  # Use different browser profile
                )

                # Disable debug mode to reduce browser issues
                self.client.debug_ws_enable = False

                # Try to connect
                check, reason = await self.client.connect()

                if check:
                    print("✅ Connected successfully!")
                    return True
                else:
                    print(f"❌ Connection failed: {reason}")

                    # Clean up failed client
                    try:
                        self.client.close()
                    except:
                        pass

                    if attempt < max_retries - 1:
                        wait_time = (attempt + 1) * 3  # Increasing wait time
                        print(f"🔄 Retrying in {wait_time} seconds...")
                        await asyncio.sleep(wait_time)

            except Exception as e:
                error_msg = str(e)
                print(f"❌ Connection error: {error_msg}")

                # Handle specific browser errors
                if "opts is not defined" in error_msg:
                    print("🔧 Browser JavaScript error detected. Trying alternative approach...")
                elif "Target page, context or browser has been closed" in error_msg:
                    print("🔧 Browser closed unexpectedly. Restarting...")
                elif "Page.evaluate" in error_msg:
                    print("🔧 Page evaluation error. Browser may be unstable...")

                # Clean up failed client
                if self.client:
                    try:
                        self.client.close()
                    except:
                        pass
                    self.client = None

                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 5  # Longer wait for errors
                    print(f"🔄 Retrying in {wait_time} seconds...")
                    await asyncio.sleep(wait_time)
                else:
                    print("❌ All connection attempts failed!")
                    print("💡 Try these solutions:")
                    print("   1. Check your internet connection")
                    print("   2. Verify credentials in quotexapi/config.py")
                    print("   3. Run: python -m playwright install firefox")
                    print("   4. Try running: python app.py test_connection")
                    return False

        return False

    def load_model(self):
        """Load trained model and metadata"""
        print("🧠 Loading trained model...")

        if not os.path.exists(CONFIG["model_path"]):
            print(f"❌ Model not found: {CONFIG['model_path']}")
            print("Please run train_price_action_model.py first!")
            return False

        # Load model
        self.model = tf.keras.models.load_model(CONFIG["model_path"])
        print("✅ Model loaded successfully!")

        # Load metadata
        if os.path.exists(CONFIG["metadata_path"]):
            with open(CONFIG["metadata_path"], 'rb') as f:
                metadata = pickle.load(f)
                self.scaler = metadata['scaler']
                self.feature_columns = metadata['feature_columns']
                self.sequence_length = metadata['sequence_length']
            print("✅ Metadata loaded successfully!")
            print(f"📊 Using {len(self.feature_columns)} features from training")
        else:
            print("⚠️ Metadata not found, using basic OHLC features")
            # Use only basic OHLC features that the model was likely trained with
            self.feature_columns = ['open', 'high', 'low', 'close']
            self.scaler = MinMaxScaler()
            print("⚠️ Will fit scaler on first prediction")

        return True

    async def get_recent_candles(self, count=100):
        """Get recent candles for prediction"""
        print(f"📊 Fetching recent {count} candles...")

        import time
        end_time = time.time()
        offset = count * CONFIG["period"]  # Get enough data

        candles = await self.client.get_candles(
            asset=CONFIG["asset"],
            end_from_time=end_time,
            offset=offset,
            period=CONFIG["period"]
        )

        if candles:
            print(f"✅ Got {len(candles)} candles")
            return candles
        else:
            print("❌ No candles received")
            return []

    def calculate_features(self, df):
        """Calculate same features as training"""
        print("🔧 Calculating features...")

        # Basic OHLC features
        df['body_size'] = abs(df['close'] - df['open'])
        df['upper_shadow'] = df['high'] - df[['open', 'close']].max(axis=1)
        df['lower_shadow'] = df[['open', 'close']].min(axis=1) - df['low']
        df['total_range'] = df['high'] - df['low']

        # Candle patterns
        df['is_bullish'] = (df['close'] > df['open']).astype(int)
        df['is_bearish'] = (df['close'] < df['open']).astype(int)
        df['is_doji'] = (df['body_size'] < df['total_range'] * 0.1).astype(int)

        # Body to shadow ratios
        df['body_to_range'] = df['body_size'] / (df['total_range'] + 1e-8)
        df['upper_shadow_ratio'] = df['upper_shadow'] / (df['total_range'] + 1e-8)
        df['lower_shadow_ratio'] = df['lower_shadow'] / (df['total_range'] + 1e-8)

        # Price momentum
        df['price_change'] = df['close'].pct_change()
        df['high_change'] = df['high'].pct_change()
        df['low_change'] = df['low'].pct_change()

        # Support/Resistance levels
        window = 20
        df['resistance_level'] = df['high'].rolling(window).max()
        df['support_level'] = df['low'].rolling(window).min()
        df['distance_to_resistance'] = (df['resistance_level'] - df['close']) / df['close']
        df['distance_to_support'] = (df['close'] - df['support_level']) / df['close']

        # Volatility
        df['volatility'] = df['total_range'].rolling(window).std()
        df['avg_body_size'] = df['body_size'].rolling(window).mean()

        # Price position in range
        df['position_in_range'] = (df['close'] - df['low']) / (df['total_range'] + 1e-8)

        # Consecutive patterns
        df['consecutive_bullish'] = (df['is_bullish'] * (df['is_bullish'].groupby((df['is_bullish'] != df['is_bullish'].shift()).cumsum()).cumcount() + 1))
        df['consecutive_bearish'] = (df['is_bearish'] * (df['is_bearish'].groupby((df['is_bearish'] != df['is_bearish'].shift()).cumsum()).cumcount() + 1))

        # Fill NaN values
        df = df.bfill().fillna(0)

        return df

    def predict(self, candles_data):
        """Make prediction on recent candles"""
        print("🔮 Making prediction...")

        # Convert to DataFrame
        df = pd.DataFrame(candles_data)
        df = df.sort_values('time').reset_index(drop=True)

        # Calculate features
        df = self.calculate_features(df)

        # Use only the features that were used in training
        if self.feature_columns:
            # Ensure all required columns exist
            missing_cols = set(self.feature_columns) - set(df.columns)
            if missing_cols:
                print(f"⚠️ Missing columns: {missing_cols}")
                # For basic OHLC, we should have these columns
                if missing_cols == set(self.feature_columns):
                    print("❌ No OHLC columns found in data")
                    return None

            # Use only the available feature columns
            available_features = [col for col in self.feature_columns if col in df.columns]
            X = df[available_features].values
            print(f"📊 Using {len(available_features)} features: {available_features}")
        else:
            # Use all numeric columns except time
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            exclude_cols = ['time']
            feature_cols = [col for col in numeric_cols if col not in exclude_cols]
            X = df[feature_cols].values
            print(f"📊 Using {len(feature_cols)} features")

        # Scale features
        if self.scaler:
            # Check if scaler needs to be fitted (for basic OHLC case)
            if not hasattr(self.scaler, 'scale_'):
                print("🔧 Fitting scaler on current data...")
                X_scaled = self.scaler.fit_transform(X)
            else:
                X_scaled = self.scaler.transform(X)
        else:
            print("⚠️ No scaler available, using raw features")
            X_scaled = X

        # Create sequence for prediction (use last sequence_length candles)
        if len(X_scaled) >= self.sequence_length:
            X_sequence = X_scaled[-self.sequence_length:].reshape(1, self.sequence_length, -1)
        else:
            print(f"⚠️ Not enough data. Need {self.sequence_length}, got {len(X_scaled)}")
            return None

        # Make prediction
        prediction = self.model.predict(X_sequence, verbose=0)
        probabilities = prediction[0]

        # Get predicted class and confidence
        predicted_class = np.argmax(probabilities)
        confidence = np.max(probabilities)

        # Map classes to directions
        direction_map = {0: "NEUTRAL", 1: "UP", 2: "DOWN"}
        predicted_direction = direction_map[predicted_class]

        result = {
            "direction": predicted_direction,
            "confidence": float(confidence),
            "probabilities": {
                "NEUTRAL": float(probabilities[0]),
                "UP": float(probabilities[1]),
                "DOWN": float(probabilities[2])
            },
            "timestamp": datetime.now().isoformat(),
            "asset": CONFIG["asset"],
            "current_price": float(df['close'].iloc[-1])
        }

        return result

    def close(self):
        if self.client:
            self.client.close()

# ============================================================================
# 🎯 MAIN EXECUTION
# ============================================================================

async def main():
    """Main prediction pipeline"""
    print("🔮 QUOTEX AI PRICE ACTION PREDICTOR")
    print("=" * 50)

    predictor = PriceActionPredictor()

    try:
        # 1. Load model
        if not predictor.load_model():
            return

        # 2. Connect to API
        if not await predictor.connect():
            return

        # 3. Get recent data and predict
        while True:
            try:
                print(f"\n📊 Analyzing {CONFIG['asset']}...")

                # Get recent candles
                candles = await predictor.get_recent_candles(100)

                if not candles:
                    print("❌ No data available")
                    await asyncio.sleep(30)
                    continue

                # Make prediction
                prediction = predictor.predict(candles)

                if prediction:
                    print("\n🎯 PREDICTION RESULT:")
                    print("=" * 30)
                    print(f"Asset: {prediction['asset']}")
                    print(f"Current Price: {prediction['current_price']:.5f}")
                    print(f"Direction: {prediction['direction']}")
                    print(f"Confidence: {prediction['confidence']:.2%}")
                    print(f"Probabilities:")
                    for direction, prob in prediction['probabilities'].items():
                        print(f"  {direction}: {prob:.2%}")

                    # Trading signal
                    if prediction['confidence'] >= CONFIG['confidence_threshold']:
                        if prediction['direction'] == "UP":
                            print("🟢 STRONG BUY SIGNAL!")
                        elif prediction['direction'] == "DOWN":
                            print("🔴 STRONG SELL SIGNAL!")
                        else:
                            print("🟡 NEUTRAL - NO TRADE")
                    else:
                        print("⚪ LOW CONFIDENCE - NO TRADE")

                    # Save prediction
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    with open(f"predictions/prediction_{timestamp}.json", 'w') as f:
                        os.makedirs("predictions", exist_ok=True)
                        json.dump(prediction, f, indent=2)

                # Wait before next prediction
                print(f"\n⏰ Waiting 60 seconds for next prediction...")
                await asyncio.sleep(60)

            except KeyboardInterrupt:
                print("\n👋 Stopping predictor...")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                await asyncio.sleep(30)

    finally:
        predictor.close()

if __name__ == "__main__":
    asyncio.run(main())
