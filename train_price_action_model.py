"""
🧠 QUOTEX AI PRICE ACTION TRAINER
================================

Fetches 30 days historical data and trains advanced price action model.
No indicators - pure candlestick psychology and price action patterns.
"""

import os
import time
import json
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# ML Libraries
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    from sklearn.preprocessing import MinMaxScaler
    from sklearn.model_selection import train_test_split
    print("✅ TensorFlow and ML libraries loaded")
except ImportError as e:
    print(f"❌ Missing ML libraries: {e}")
    print("Install with: pip install tensorflow scikit-learn pandas numpy")
    exit(1)

# Quotex API
try:
    from quotexapi.stable_api import Quotex
    from quotexapi.expiration import get_timestamp_days_ago, timestamp_to_date
    from quotexapi.config import email, password
    print("✅ Quotex API loaded")
except ImportError as e:
    print(f"❌ Quotex API not found: {e}")
    exit(1)

# ============================================================================
# 🔧 CONFIGURATION
# ============================================================================

CONFIG = {
    "email": email,
    "password": password,
    "asset": "EURUSD_otc",  # Change to any asset
    "period": 60,  # 1-minute candles
    "days": 30,
    "sequence_length": 60,  # Look back 60 candles
    "prediction_horizon": 1,  # Predict next 1 candle
    "model_path": "models/",
    "data_path": "data/"
}

# ============================================================================
# 📊 DATA FETCHER CLASS
# ============================================================================

class DataFetcher:

    def __init__(self):
        self.client = None

    async def connect(self, max_retries=3):
        """Connect to Quotex API with retry logic"""
        print("🔌 Connecting to Quotex...")

        for attempt in range(max_retries):
            try:
                print(f"Attempt {attempt + 1}/{max_retries}")

                self.client = Quotex(
                    email=CONFIG["email"],
                    password=CONFIG["password"],
                    lang="en"
                )

                # Set longer timeout and disable headless mode if needed
                self.client.debug_ws_enable = False

                check, reason = await self.client.connect()
                if check:
                    print("✅ Connected successfully!")
                    return True
                else:
                    print(f"❌ Connection failed: {reason}")
                    if attempt < max_retries - 1:
                        print("🔄 Retrying in 5 seconds...")
                        await asyncio.sleep(5)

            except Exception as e:
                print(f"❌ Connection error: {e}")
                if attempt < max_retries - 1:
                    print("🔄 Retrying in 5 seconds...")
                    await asyncio.sleep(5)
                else:
                    print("❌ All connection attempts failed!")
                    return False

        return False

    async def fetch_historical_data(self, asset, period, days):
        """Fetch 30 days historical data"""
        print(f"📈 Fetching {days} days data for {asset}...")

        all_candles = []
        offset = 3600  # 1 hour chunks
        size = days * 24

        start_timestamp = get_timestamp_days_ago(days)
        end_from_time = (int(start_timestamp) - int(start_timestamp) % period) + offset

        for i in range(size):
            try:
                print(f"🔄 Fetching chunk {i+1}/{size}", end="\r")

                candles = await self.client.get_candles(
                    asset=asset,
                    end_from_time=end_from_time,
                    offset=offset,
                    period=period,
                    progressive=True
                )

                if candles:
                    all_candles.extend(candles)

                end_from_time += offset
                await asyncio.sleep(0.05)  # Rate limiting

            except Exception as e:
                print(f"\n⚠️ Error in chunk {i+1}: {e}")
                continue

        # Remove duplicates and sort by time
        unique_candles = list({frozenset(d.items()): d for d in all_candles}.values())
        unique_candles.sort(key=lambda x: x.get('time', 0))

        print(f"\n✅ Fetched {len(unique_candles)} unique candles")
        return unique_candles

    def close(self):
        if self.client:
            self.client.close()

# ============================================================================
# 🧠 PRICE ACTION FEATURE ENGINEER
# ============================================================================

class PriceActionFeatures:

    @staticmethod
    def calculate_features(df):
        """Calculate advanced price action features"""
        print("🔧 Engineering price action features...")

        # Basic OHLC features
        df['body_size'] = abs(df['close'] - df['open'])
        df['upper_shadow'] = df['high'] - df[['open', 'close']].max(axis=1)
        df['lower_shadow'] = df[['open', 'close']].min(axis=1) - df['low']
        df['total_range'] = df['high'] - df['low']

        # Candle patterns
        df['is_bullish'] = (df['close'] > df['open']).astype(int)
        df['is_bearish'] = (df['close'] < df['open']).astype(int)
        df['is_doji'] = (df['body_size'] < df['total_range'] * 0.1).astype(int)

        # Body to shadow ratios
        df['body_to_range'] = df['body_size'] / (df['total_range'] + 1e-8)
        df['upper_shadow_ratio'] = df['upper_shadow'] / (df['total_range'] + 1e-8)
        df['lower_shadow_ratio'] = df['lower_shadow'] / (df['total_range'] + 1e-8)

        # Price momentum
        df['price_change'] = df['close'].pct_change()
        df['high_change'] = df['high'].pct_change()
        df['low_change'] = df['low'].pct_change()

        # Support/Resistance levels (simplified)
        window = 20
        df['resistance_level'] = df['high'].rolling(window).max()
        df['support_level'] = df['low'].rolling(window).min()
        df['distance_to_resistance'] = (df['resistance_level'] - df['close']) / df['close']
        df['distance_to_support'] = (df['close'] - df['support_level']) / df['close']

        # Volatility
        df['volatility'] = df['total_range'].rolling(window).std()
        df['avg_body_size'] = df['body_size'].rolling(window).mean()

        # Price position in range
        df['position_in_range'] = (df['close'] - df['low']) / (df['total_range'] + 1e-8)

        # Consecutive patterns
        df['consecutive_bullish'] = (df['is_bullish'] * (df['is_bullish'].groupby((df['is_bullish'] != df['is_bullish'].shift()).cumsum()).cumcount() + 1))
        df['consecutive_bearish'] = (df['is_bearish'] * (df['is_bearish'].groupby((df['is_bearish'] != df['is_bearish'].shift()).cumsum()).cumcount() + 1))

        # Fill NaN values (using newer pandas syntax)
        df = df.bfill().fillna(0)

        return df

    @staticmethod
    def create_labels(df, horizon=1):
        """Create labels for next price movement"""
        print("🎯 Creating prediction labels...")

        # Future price movement
        df['future_close'] = df['close'].shift(-horizon)
        df['price_movement'] = (df['future_close'] - df['close']) / df['close']

        # Classification labels
        threshold = 0.0001  # 0.01% threshold
        df['direction'] = 0  # NEUTRAL
        df.loc[df['price_movement'] > threshold, 'direction'] = 1  # UP
        df.loc[df['price_movement'] < -threshold, 'direction'] = 2  # DOWN

        # Remove last rows without future data
        df = df[:-horizon]

        return df

# ============================================================================
# 🤖 LSTM PRICE ACTION MODEL
# ============================================================================

class PriceActionModel:

    def __init__(self, sequence_length=60):
        self.sequence_length = sequence_length
        self.model = None
        self.scaler = MinMaxScaler()
        self.feature_columns = None

    def prepare_data(self, df):
        """Prepare data for LSTM training"""
        print("📊 Preparing data for training...")

        # Select only numeric features (exclude target, time, and non-numeric columns)
        exclude_cols = ['time', 'direction', 'future_close', 'price_movement', 'datetime']

        # Get all columns and filter out non-numeric ones
        all_cols = df.columns.tolist()
        numeric_cols = []

        for col in all_cols:
            if col not in exclude_cols:
                # Check if column is numeric
                try:
                    pd.to_numeric(df[col], errors='raise')
                    numeric_cols.append(col)
                except (ValueError, TypeError):
                    print(f"⚠️ Skipping non-numeric column: {col}")
                    continue

        self.feature_columns = numeric_cols
        print(f"📊 Using {len(self.feature_columns)} numeric features")

        # Features and labels
        X = df[self.feature_columns].values.astype(float)
        y = df['direction'].values

        # Check for any remaining NaN or infinite values
        if np.any(np.isnan(X)) or np.any(np.isinf(X)):
            print("⚠️ Found NaN or infinite values, cleaning data...")
            X = np.nan_to_num(X, nan=0.0, posinf=1e6, neginf=-1e6)

        # Scale features
        X_scaled = self.scaler.fit_transform(X)

        # Create sequences
        X_sequences, y_sequences = [], []

        for i in range(self.sequence_length, len(X_scaled)):
            X_sequences.append(X_scaled[i-self.sequence_length:i])
            y_sequences.append(y[i])

        X_sequences = np.array(X_sequences)
        y_sequences = np.array(y_sequences)

        print(f"✅ Data shape: X={X_sequences.shape}, y={y_sequences.shape}")

        return X_sequences, y_sequences

    def build_model(self, input_shape, num_classes=3):
        """Build advanced LSTM model"""
        print("🏗️ Building LSTM model...")

        model = Sequential([
            # First LSTM layer
            LSTM(128, return_sequences=True, input_shape=input_shape),
            BatchNormalization(),
            Dropout(0.3),

            # Second LSTM layer
            LSTM(64, return_sequences=True),
            BatchNormalization(),
            Dropout(0.3),

            # Third LSTM layer
            LSTM(32, return_sequences=False),
            BatchNormalization(),
            Dropout(0.3),

            # Dense layers
            Dense(64, activation='relu'),
            BatchNormalization(),
            Dropout(0.2),

            Dense(32, activation='relu'),
            Dropout(0.2),

            # Output layer
            Dense(num_classes, activation='softmax')
        ])

        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )

        self.model = model
        print("✅ Model built successfully!")
        return model

    def train(self, X, y, validation_split=0.2, epochs=100):
        """Train the model"""
        print("🚀 Starting training...")

        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=validation_split, random_state=42, stratify=y
        )

        # Callbacks
        callbacks = [
            EarlyStopping(patience=15, restore_best_weights=True),
            ReduceLROnPlateau(patience=10, factor=0.5, min_lr=1e-7)
        ]

        # Train model
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=32,
            callbacks=callbacks,
            verbose=1
        )

        print("✅ Training completed!")
        return history

    def save_model(self, filepath):
        """Save model and scaler"""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # Save model
        self.model.save(filepath)

        # Save scaler and metadata
        metadata = {
            'scaler': self.scaler,
            'feature_columns': self.feature_columns,
            'sequence_length': self.sequence_length
        }

        import pickle
        with open(filepath.replace('.h5', '_metadata.pkl'), 'wb') as f:
            pickle.dump(metadata, f)

        print(f"💾 Model saved to {filepath}")

# ============================================================================
# 🎯 MAIN EXECUTION
# ============================================================================

async def main():
    """Main training pipeline"""
    print("🧠 QUOTEX AI PRICE ACTION TRAINER")
    print("=" * 50)

    # Create directories
    os.makedirs(CONFIG["model_path"], exist_ok=True)
    os.makedirs(CONFIG["data_path"], exist_ok=True)

    # 1. Fetch Data
    fetcher = DataFetcher()
    try:
        if not await fetcher.connect():
            return

        raw_data = await fetcher.fetch_historical_data(
            CONFIG["asset"],
            CONFIG["period"],
            CONFIG["days"]
        )

        if not raw_data:
            print("❌ No data fetched!")
            return

        # Save raw data
        with open(f"{CONFIG['data_path']}/raw_data_{CONFIG['asset']}.json", 'w') as f:
            json.dump(raw_data, f, indent=2)

    finally:
        fetcher.close()

    # 2. Process Data
    print("\n📊 Processing data...")

    # Handle different data formats from progressive API
    if isinstance(raw_data, dict) and 'data' in raw_data:
        # If data comes in nested format
        candle_data = raw_data['data']
    elif isinstance(raw_data, list):
        # If data comes as list
        candle_data = raw_data
    else:
        print("❌ Unexpected data format!")
        return

    # Convert to DataFrame
    df = pd.DataFrame(candle_data)

    # Check if we have the required columns
    required_cols = ['time', 'open', 'high', 'low', 'close']
    missing_cols = [col for col in required_cols if col not in df.columns]

    if missing_cols:
        print(f"❌ Missing required columns: {missing_cols}")
        print(f"Available columns: {df.columns.tolist()}")
        return

    # Ensure numeric columns are properly typed
    numeric_cols = ['open', 'high', 'low', 'close', 'time']
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')

    # Remove rows with NaN values in essential columns
    df = df.dropna(subset=required_cols)

    # Convert timestamp to datetime
    df['datetime'] = pd.to_datetime(df['time'], unit='s')
    df = df.sort_values('time').reset_index(drop=True)

    print(f"Data shape: {df.shape}")
    print(f"Date range: {df['datetime'].min()} to {df['datetime'].max()}")
    print(f"Sample data:\n{df.head()}")

    # 3. Feature Engineering
    features = PriceActionFeatures()
    df = features.calculate_features(df)
    df = features.create_labels(df, CONFIG["prediction_horizon"])

    # Save processed data
    df.to_csv(f"{CONFIG['data_path']}/processed_data_{CONFIG['asset']}.csv", index=False)

    # 4. Train Model
    model = PriceActionModel(CONFIG["sequence_length"])
    X, y = model.prepare_data(df)

    # Check class distribution
    unique, counts = np.unique(y, return_counts=True)
    print(f"Class distribution: {dict(zip(unique, counts))}")

    # Build and train model
    model.build_model((X.shape[1], X.shape[2]))
    history = model.train(X, y)

    # 5. Save Model
    model_filename = f"{CONFIG['model_path']}/price_action_model_{CONFIG['asset']}_{CONFIG['period']}s.h5"
    model.save_model(model_filename)

    # 6. Training Summary
    print("\n🎉 TRAINING COMPLETED!")
    print("=" * 50)
    print(f"Asset: {CONFIG['asset']}")
    print(f"Timeframe: {CONFIG['period']}s")
    print(f"Data points: {len(df)}")
    print(f"Features: {len(model.feature_columns)}")
    print(f"Model saved: {model_filename}")

    final_accuracy = max(history.history['val_accuracy'])
    print(f"Best validation accuracy: {final_accuracy:.4f}")

if __name__ == "__main__":
    asyncio.run(main())
