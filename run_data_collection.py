"""
🚀 SIMPLE DATA COLLECTION RUNNER 🚀
====================================

Easy-to-use script to collect training data using Quotex API
Just run this script and enter your credentials!

Author: Trading Bot AI
Version: 1.0.0 - Simple Runner
License: MIT
"""

import asyncio
import os
from fetch_training_data import TrainingDataFetcher

def get_credentials():
    """
    Get Quotex credentials from user
    
    Returns:
        tuple: (email, password)
    """
    print("🔐 QUOTEX CREDENTIALS REQUIRED")
    print("=" * 40)
    print("📧 Please enter your Quotex account credentials")
    print("⚠️  This will be used to fetch real trading data")
    print("🛡️  Your credentials are NOT stored anywhere")
    print("=" * 40)
    
    email = input("📧 Quotex Email: ").strip()
    password = input("🔐 Quotex Password: ").strip()
    
    if not email or not password:
        print("❌ Both email and password are required!")
        return None, None
    
    return email, password

async def main():
    """
    Main data collection function
    """
    print("🧠 BRAIN MODEL TRAINING DATA COLLECTOR")
    print("=" * 50)
    print("🎯 This script will collect 20 days of data for 10 assets")
    print("📊 Assets: EURUSD, GBPUSD, USDJPY, AUDUSD, USDCAD,")
    print("          BTCUSD, ETHUSD, XRPUSD, MSFT, GOOGL")
    print("🔗 Data source: Quotex API (real trading data)")
    print("💾 Output: Raw + Processed data for each asset")
    print("🚀 Ready for Google Colab training!")
    print("=" * 50)
    
    # Get credentials
    email, password = get_credentials()
    if not email or not password:
        return
    
    print(f"\n🔄 Starting data collection...")
    print("⏳ This may take 5-10 minutes depending on connection speed")
    
    try:
        # Initialize fetcher
        fetcher = TrainingDataFetcher(email=email, password=password)
        
        # Collect data
        results = await fetcher.fetch_all_assets()
        
        if results and results['successful']:
            print("\n🎉 DATA COLLECTION COMPLETED SUCCESSFULLY!")
            print("=" * 50)
            print(f"✅ Successfully collected: {len(results['successful'])} assets")
            print(f"❌ Failed to collect: {len(results['failed'])} assets")
            
            if results['successful']:
                print(f"\n📊 SUCCESSFUL ASSETS:")
                for asset in results['successful']:
                    summary = results['summary'][asset]
                    print(f"   📈 {asset}: {summary['raw_points']:,} data points")
                    print(f"      📅 {summary['date_range']['start']} to {summary['date_range']['end']}")
                    labels = summary['label_distribution']
                    print(f"      📊 DOWN: {labels['DOWN']}, NEUTRAL: {labels['NEUTRAL']}, UP: {labels['UP']}")
            
            if results['failed']:
                print(f"\n❌ FAILED ASSETS:")
                for asset in results['failed']:
                    print(f"   💥 {asset}")
            
            print(f"\n📁 DATA SAVED TO:")
            print(f"   📂 Raw data: {fetcher.raw_data_dir}")
            print(f"   🔧 Processed data: {fetcher.processed_data_dir}")
            print(f"   📋 Summary: {os.path.join(fetcher.data_dir, 'collection_summary.json')}")
            
            print(f"\n🚀 NEXT STEPS:")
            print("1. 📦 Zip the 'training_data' folder")
            print("2. 📤 Upload to Google Colab")
            print("3. 🧠 Train your brain model!")
            print("4. 💾 Download trained model")
            print("5. 🎯 Deploy to your trading bot")
            
        else:
            print("\n❌ DATA COLLECTION FAILED!")
            print("🔍 Check your internet connection and Quotex credentials")
            
    except KeyboardInterrupt:
        print("\n⚠️ Data collection interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during data collection: {str(e)}")
        print("🔍 Please check your credentials and try again")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        print("🔍 Please report this issue if it persists")
    
    input("\n⏸️ Press Enter to exit...")
