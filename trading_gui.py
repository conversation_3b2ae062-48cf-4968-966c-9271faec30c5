"""
🎨 QUOTEX TRADING BOT GUI
========================

Beautiful, minimal dashboard showing balance, win rate, active trades, and statistics.
"""

import tkinter as tk
from tkinter import ttk
import customtkinter as ctk
from datetime import datetime
import threading
import asyncio

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class TradingDashboard:
    """Beautiful minimal trading dashboard"""
    
    def __init__(self, trading_engine):
        self.trading_engine = trading_engine
        self.root = ctk.CTk()
        self.root.title("🤖 Quotex AI Trading Bot")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # Colors
        self.colors = {
            "bg_primary": "#1a1a1a",
            "bg_secondary": "#2d2d2d", 
            "accent": "#00d4aa",
            "success": "#00ff88",
            "danger": "#ff4757",
            "warning": "#ffa502",
            "text_primary": "#ffffff",
            "text_secondary": "#b8b8b8"
        }
        
        # Variables for real-time updates
        self.balance_var = tk.StringVar(value="$0.00")
        self.win_rate_var = tk.StringVar(value="0%")
        self.profit_var = tk.StringVar(value="$0.00")
        self.status_var = tk.StringVar(value="Disconnected")
        self.signal_var = tk.StringVar(value="NEUTRAL")
        self.confidence_var = tk.StringVar(value="0%")
        
        self.setup_ui()
        self.start_update_loop()
    
    def setup_ui(self):
        """Setup the user interface"""
        # Main container
        main_frame = ctk.CTkFrame(self.root, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Top section - Status and Controls
        self.create_top_section(main_frame)
        
        # Middle section - Key metrics
        self.create_metrics_section(main_frame)
        
        # Bottom section - Trading controls and logs
        self.create_bottom_section(main_frame)
    
    def create_top_section(self, parent):
        """Create top status section"""
        top_frame = ctk.CTkFrame(parent, height=80)
        top_frame.pack(fill="x", pady=(0, 20))
        top_frame.pack_propagate(False)
        
        # Status indicator
        status_frame = ctk.CTkFrame(top_frame, fg_color="transparent")
        status_frame.pack(side="left", fill="y", padx=20, pady=15)
        
        ctk.CTkLabel(
            status_frame, 
            text="Status:", 
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(anchor="w")
        
        self.status_label = ctk.CTkLabel(
            status_frame,
            textvariable=self.status_var,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors["danger"]
        )
        self.status_label.pack(anchor="w")
        
        # Control buttons
        controls_frame = ctk.CTkFrame(top_frame, fg_color="transparent")
        controls_frame.pack(side="right", fill="y", padx=20, pady=15)
        
        self.connect_btn = ctk.CTkButton(
            controls_frame,
            text="Connect",
            command=self.toggle_connection,
            width=100,
            height=35,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.connect_btn.pack(side="left", padx=(0, 10))
        
        self.trading_btn = ctk.CTkButton(
            controls_frame,
            text="Start Trading",
            command=self.toggle_trading,
            width=120,
            height=35,
            font=ctk.CTkFont(size=14, weight="bold"),
            state="disabled"
        )
        self.trading_btn.pack(side="left")
    
    def create_metrics_section(self, parent):
        """Create key metrics display"""
        metrics_frame = ctk.CTkFrame(parent, height=200)
        metrics_frame.pack(fill="x", pady=(0, 20))
        metrics_frame.pack_propagate(False)
        
        # Balance card
        balance_card = self.create_metric_card(
            metrics_frame, "Balance", self.balance_var, self.colors["accent"]
        )
        balance_card.pack(side="left", fill="both", expand=True, padx=(20, 10), pady=20)
        
        # Win Rate card
        winrate_card = self.create_metric_card(
            metrics_frame, "Win Rate", self.win_rate_var, self.colors["success"]
        )
        winrate_card.pack(side="left", fill="both", expand=True, padx=(10, 10), pady=20)
        
        # Profit card
        profit_card = self.create_metric_card(
            metrics_frame, "Today's Profit", self.profit_var, self.colors["warning"]
        )
        profit_card.pack(side="left", fill="both", expand=True, padx=(10, 20), pady=20)
    
    def create_metric_card(self, parent, title, value_var, color):
        """Create a metric display card"""
        card = ctk.CTkFrame(parent)
        
        # Title
        ctk.CTkLabel(
            card,
            text=title,
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors["text_secondary"]
        ).pack(pady=(20, 5))
        
        # Value
        ctk.CTkLabel(
            card,
            textvariable=value_var,
            font=ctk.CTkFont(size=32, weight="bold"),
            text_color=color
        ).pack(pady=(0, 20))
        
        return card
    
    def create_bottom_section(self, parent):
        """Create bottom section with trading info and controls"""
        bottom_frame = ctk.CTkFrame(parent)
        bottom_frame.pack(fill="both", expand=True)
        
        # Left side - Current signal and settings
        left_frame = ctk.CTkFrame(bottom_frame, width=400)
        left_frame.pack(side="left", fill="y", padx=(20, 10), pady=20)
        left_frame.pack_propagate(False)
        
        self.create_signal_section(left_frame)
        self.create_settings_section(left_frame)
        
        # Right side - Active trades and logs
        right_frame = ctk.CTkFrame(bottom_frame)
        right_frame.pack(side="right", fill="both", expand=True, padx=(10, 20), pady=20)
        
        self.create_trades_section(right_frame)
    
    def create_signal_section(self, parent):
        """Create current signal display"""
        signal_frame = ctk.CTkFrame(parent)
        signal_frame.pack(fill="x", padx=20, pady=(20, 10))
        
        ctk.CTkLabel(
            signal_frame,
            text="Current Signal",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(15, 5))
        
        # Signal display
        signal_display = ctk.CTkFrame(signal_frame, fg_color="transparent")
        signal_display.pack(fill="x", padx=20, pady=(0, 15))
        
        self.signal_label = ctk.CTkLabel(
            signal_display,
            textvariable=self.signal_var,
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=self.colors["accent"]
        )
        self.signal_label.pack()
        
        self.confidence_label = ctk.CTkLabel(
            signal_display,
            textvariable=self.confidence_var,
            font=ctk.CTkFont(size=14),
            text_color=self.colors["text_secondary"]
        )
        self.confidence_label.pack()
    
    def create_settings_section(self, parent):
        """Create trading settings"""
        settings_frame = ctk.CTkFrame(parent)
        settings_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(
            settings_frame,
            text="Trading Settings",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(15, 10))
        
        # Asset selection
        asset_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        asset_frame.pack(fill="x", padx=20, pady=5)
        
        ctk.CTkLabel(asset_frame, text="Asset:", width=80).pack(side="left")
        self.asset_combo = ctk.CTkComboBox(
            asset_frame,
            values=["EURUSD_otc", "GBPUSD_otc", "USDJPY_otc"],
            width=200
        )
        self.asset_combo.pack(side="right")
        self.asset_combo.set("EURUSD_otc")
        
        # Timeframe selection
        timeframe_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        timeframe_frame.pack(fill="x", padx=20, pady=5)
        
        ctk.CTkLabel(timeframe_frame, text="Timeframe:", width=80).pack(side="left")
        self.timeframe_combo = ctk.CTkComboBox(
            timeframe_frame,
            values=["60s", "90s"],
            width=200
        )
        self.timeframe_combo.pack(side="right")
        self.timeframe_combo.set("60s")
        
        # Trade amount
        amount_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        amount_frame.pack(fill="x", padx=20, pady=(5, 15))
        
        ctk.CTkLabel(amount_frame, text="Amount:", width=80).pack(side="left")
        self.amount_entry = ctk.CTkEntry(amount_frame, width=200)
        self.amount_entry.pack(side="right")
        self.amount_entry.insert(0, "1.0")
    
    def create_trades_section(self, parent):
        """Create active trades and statistics section"""
        # Title
        ctk.CTkLabel(
            parent,
            text="Active Trades & Statistics",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(20, 10))
        
        # Trades display
        self.trades_text = ctk.CTkTextbox(
            parent,
            height=300,
            font=ctk.CTkFont(family="Consolas", size=12)
        )
        self.trades_text.pack(fill="both", expand=True, padx=20, pady=(0, 20))
    
    def toggle_connection(self):
        """Toggle connection to Quotex"""
        if not self.trading_engine.is_connected:
            # Start connection in background
            threading.Thread(target=self.connect_async, daemon=True).start()
        else:
            self.trading_engine.close()
            self.update_connection_status(False)
    
    def connect_async(self):
        """Connect to Quotex asynchronously"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Load models first
            if not self.trading_engine.load_models():
                self.show_error("Failed to load ML models")
                return
            
            # Connect to API
            success = loop.run_until_complete(self.trading_engine.connect())
            self.update_connection_status(success)
            
        except Exception as e:
            self.show_error(f"Connection failed: {e}")
        finally:
            loop.close()
    
    def toggle_trading(self):
        """Toggle automated trading"""
        if not self.trading_engine.is_trading:
            self.trading_engine.start_trading()
            self.trading_btn.configure(text="Stop Trading")
        else:
            self.trading_engine.stop_trading()
            self.trading_btn.configure(text="Start Trading")
    
    def update_connection_status(self, connected):
        """Update connection status display"""
        if connected:
            self.status_var.set("Connected")
            self.status_label.configure(text_color=self.colors["success"])
            self.connect_btn.configure(text="Disconnect")
            self.trading_btn.configure(state="normal")
        else:
            self.status_var.set("Disconnected")
            self.status_label.configure(text_color=self.colors["danger"])
            self.connect_btn.configure(text="Connect")
            self.trading_btn.configure(state="disabled")
            self.trading_btn.configure(text="Start Trading")
    
    def show_error(self, message):
        """Show error message"""
        # You can implement a custom error dialog here
        print(f"Error: {message}")
    
    def start_update_loop(self):
        """Start the real-time update loop"""
        self.update_display()
        self.root.after(1000, self.start_update_loop)  # Update every second
    
    def update_display(self):
        """Update all display elements"""
        if self.trading_engine.is_connected:
            # Update balance (you'll need to implement async balance fetching)
            # For now, using placeholder
            self.balance_var.set("$1,234.56")
            
            # Update statistics
            stats = self.trading_engine.get_trading_stats()
            self.win_rate_var.set(f"{stats['win_rate']:.1f}%")
            self.profit_var.set(f"${stats['profit']:.2f}")
            
            # Update active trades display
            self.update_trades_display()
    
    def update_trades_display(self):
        """Update the trades display"""
        active_trades = self.trading_engine.get_active_trades()
        stats = self.trading_engine.get_trading_stats()
        
        # Clear and update text
        self.trades_text.delete("1.0", "end")
        
        # Statistics
        self.trades_text.insert("end", "=== TRADING STATISTICS ===\n")
        self.trades_text.insert("end", f"Total Trades: {stats['total_trades']}\n")
        self.trades_text.insert("end", f"Wins: {stats['wins']} | Losses: {stats['losses']}\n")
        self.trades_text.insert("end", f"Win Rate: {stats['win_rate']:.1f}%\n")
        self.trades_text.insert("end", f"Profit: ${stats['profit']:.2f}\n")
        self.trades_text.insert("end", f"Daily Trades: {stats['daily_trades']}\n\n")
        
        # Active trades
        self.trades_text.insert("end", "=== ACTIVE TRADES ===\n")
        if active_trades:
            for trade in active_trades:
                elapsed = datetime.now() - trade['start_time']
                self.trades_text.insert("end", 
                    f"{trade['direction']} {trade['asset']} ${trade['amount']} "
                    f"({trade['timeframe']}s) - {elapsed.seconds}s elapsed\n"
                )
        else:
            self.trades_text.insert("end", "No active trades\n")
    
    def run(self):
        """Start the GUI"""
        self.root.mainloop()
