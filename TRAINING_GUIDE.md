# 🧠 **BRAIN MODEL TRAINING GUIDE** 🧠

## 🎯 **Complete Guide to Train Your Trading Bot Model**

This guide will help you train a neural network model using 10 different assets with 20 days of historical data.

---

## 📋 **Step-by-Step Process**

### **Step 1: Collect Training Data** 🔄

**Option A: Easy Method (Recommended)**
```bash
# Double-click this file or run in command prompt
collect_training_data.bat
```

**Option B: Python Script**
```bash
python run_data_collection.py
```

**Option C: Advanced Method**
```bash
python fetch_training_data.py
```

**What it does:**
- 🔗 Connects to Quotex API using your credentials
- 📊 Fetches 10 different assets (Forex, Crypto, Stocks)
- ⏰ Collects 20 days of 1-minute historical data per asset
- 💾 Saves both raw and processed data
- 📁 Creates a `training_data/` directory with all files
- 🛡️ Uses DEMO account for safety

**What you'll need:**
- 📧 Your Quotex account email
- 🔐 Your Quotex account password
- 🌐 Internet connection
- ⏰ 5-10 minutes for data collection

**Expected Output:**
```
training_data/
├── raw/
│   ├── EURUSD_raw_20days.csv
│   ├── EURUSD_raw_metadata.json
│   ├── BTCUSD_raw_20days.csv
│   ├── BTCUSD_raw_metadata.json
│   └── ... (more assets)
├── processed/
│   ├── EURUSD_processed_20days.csv
│   ├── EURUSD_processed_metadata.json
│   ├── BTCUSD_processed_20days.csv
│   ├── BTCUSD_processed_metadata.json
│   └── ... (more assets)
└── collection_summary.json
```

### **Step 2: Prepare for Google Colab** 📦

1. **Zip the training data:**
   ```bash
   # On Windows
   Compress-Archive -Path training_data -DestinationPath training_data.zip

   # On Linux/Mac
   zip -r training_data.zip training_data/
   ```

2. **Prepare files for upload:**
   - `training_data.zip` (your collected data)
   - `colab_training_helper.py` (training helper script)
   - `Brain_Model_Training_Colab.ipynb` (Colab notebook)

### **Step 3: Train in Google Colab** 🚀

1. **Open Google Colab:**
   - Go to [colab.research.google.com](https://colab.research.google.com)
   - Upload `Brain_Model_Training_Colab.ipynb`

2. **Enable GPU (Recommended):**
   - Runtime → Change runtime type → GPU → T4

3. **Follow the notebook:**
   - Run each cell in order
   - Upload your files when prompted
   - Wait for training to complete (15-30 minutes)

4. **Download trained model:**
   - The notebook will create `brain_model_trained.zip`
   - Download it to your computer

### **Step 4: Deploy to Trading Bot** 🎯

1. **Extract the model:**
   ```bash
   # Extract brain_model_trained.zip
   # You'll get: brain_latest.h5 and metadata files
   ```

2. **Replace old model:**
   ```bash
   # Copy to your bot directory
   cp brain_latest.h5 /path/to/your/bot/models/
   ```

3. **Test the bot:**
   ```bash
   python bot.py
   ```

---

## 🔧 **Configuration Options**

### **Assets to Train On:**

The default configuration includes:

**Forex Pairs:**
- EURUSD, GBPUSD, USDJPY, AUDUSD, USDCAD

**Cryptocurrencies:**
- BTCUSD, ETHUSD, XRPUSD

**Stocks:**
- AAPL, GOOGL

### **Customizing Assets:**

Edit `fetch_training_data.py`:

```python
self.assets = {
    # Add your preferred assets
    'CUSTOM1': 'SYMBOL1',
    'CUSTOM2': 'SYMBOL2',
    # ... up to 10 assets
}
```

### **Training Parameters:**

In the Colab notebook, you can adjust:

```python
# Model architecture
LSTM(64, return_sequences=True)  # Increase for more complexity
LSTM(32, return_sequences=False)  # Adjust layer size

# Training settings
epochs=100          # Increase for longer training
batch_size=32       # Adjust based on GPU memory
patience=15         # Early stopping patience
```

---

## 📊 **Expected Results**

### **Training Metrics:**
- **Accuracy**: 60-75% (good for financial data)
- **Training Time**: 15-30 minutes on GPU
- **Model Size**: ~2-5 MB

### **Performance Indicators:**
- **Loss**: Should decrease steadily
- **Validation Accuracy**: Should be close to training accuracy
- **No Overfitting**: Validation loss shouldn't increase while training loss decreases

### **Label Distribution:**
- **DOWN**: 30-40% (market goes down)
- **NEUTRAL**: 20-40% (market stays flat)
- **UP**: 30-40% (market goes up)

---

## 🚨 **Troubleshooting**

### **Common Issues:**

**1. "No data found for asset"**
```
Solution: Asset might not be available on Yahoo Finance
- Try different symbol format (e.g., EURUSD=X vs EUR=X)
- Check if market is open
- Use alternative data source
```

**2. "Insufficient data points"**
```
Solution: Reduce minimum data requirements
- Lower min_points in validate_data()
- Use longer time period (30+ days)
- Try different interval (5m instead of 1m)
```

**3. "Model accuracy too low"**
```
Solution: Improve training data quality
- Add more assets
- Use longer time period
- Better feature engineering
- Adjust model architecture
```

**4. "Out of memory in Colab"**
```
Solution: Reduce data size
- Use fewer assets
- Smaller batch size
- Shorter sequences
- Use CPU instead of GPU
```

### **Data Quality Checks:**

Before training, verify:
- ✅ At least 1000 data points per asset
- ✅ No excessive NaN values
- ✅ Balanced label distribution
- ✅ Reasonable price ranges

---

## 🎯 **Advanced Tips**

### **Improving Model Performance:**

1. **More Data:**
   - Increase from 20 to 30+ days
   - Add more assets (up to 20)
   - Use multiple timeframes

2. **Better Features:**
   - Add technical indicators
   - Include volume data
   - Use price ratios instead of absolute prices

3. **Model Architecture:**
   - Try bidirectional LSTM
   - Add attention layers
   - Use ensemble of models

4. **Training Techniques:**
   - Use class weights for imbalanced data
   - Implement custom loss functions
   - Add regularization

### **Production Considerations:**

1. **Model Versioning:**
   - Save models with timestamps
   - Keep backup of working models
   - Test new models on paper trading first

2. **Performance Monitoring:**
   - Track live trading accuracy
   - Monitor for model drift
   - Retrain periodically (weekly/monthly)

3. **Risk Management:**
   - Don't rely solely on model predictions
   - Use confidence thresholds
   - Implement position sizing

---

## 📈 **Success Metrics**

### **Training Success:**
- ✅ Model trains without errors
- ✅ Validation accuracy > 60%
- ✅ No severe overfitting
- ✅ Reasonable training time

### **Trading Success:**
- ✅ Live accuracy matches training accuracy
- ✅ Profitable over time
- ✅ Reasonable win rate (65%+)
- ✅ Low drawdown

---

## 🎉 **Conclusion**

Following this guide, you should be able to:

1. ✅ Collect high-quality training data
2. ✅ Train a neural network model in Google Colab
3. ✅ Deploy the model to your trading bot
4. ✅ Monitor and improve performance

**Remember:** Trading involves risk. Always test thoroughly before using real money!

---

## 📞 **Support**

If you encounter issues:
1. Check the troubleshooting section
2. Verify your data quality
3. Try with fewer assets first
4. Use the default configuration initially

**Good luck with your brain model training!** 🚀🧠
