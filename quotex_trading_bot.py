"""
🤖 QUOTEX AI TRADING BOT
========================

Advanced trading bot combining price action analysis with ML models.
Features beautiful GUI, real-time analysis, and automated trading.
"""

import os
import sys
import json
import time
import asyncio
import threading
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# GUI Libraries
try:
    import tkinter as tk
    from tkinter import ttk, messagebox
    import customtkinter as ctk
    print("✅ GUI libraries loaded")
except ImportError as e:
    print(f"❌ Missing GUI libraries: {e}")
    print("Install with: pip install customtkinter")
    sys.exit(1)

# ML Libraries
try:
    import tensorflow as tf
    import pickle
    from sklearn.preprocessing import MinMaxScaler
    print("✅ ML libraries loaded")
except ImportError as e:
    print(f"❌ Missing ML libraries: {e}")
    sys.exit(1)

# Quotex API
try:
    from quotexapi.stable_api import Quotex
    from quotexapi.config import email, password
    print("✅ Quotex API loaded")
except ImportError as e:
    print(f"❌ Quotex API not found: {e}")
    sys.exit(1)

# ============================================================================
# 🔧 CONFIGURATION
# ============================================================================

CONFIG = {
    "email": email,
    "password": password,
    "asset": "EURUSD_otc",
    "timeframes": [60, 90],  # Only 60s and 90s as requested
    "trade_amount": 1.0,
    "confidence_threshold": 90,  # Minimum confidence for trading
    "max_trades_per_hour": 10,
    "model_path": "models/brain_latest.h5",
    "sequence_length": 60,
    "risk_management": {
        "max_daily_loss": 50.0,
        "max_consecutive_losses": 3,
        "profit_target": 100.0
    }
}

# ============================================================================
# 🧠 PRICE ACTION ANALYZER
# ============================================================================

class PriceActionAnalyzer:
    """Pure rule-based price action analysis with candlestick patterns"""

    def __init__(self):
        self.patterns = {
            'bullish_engulfing': self._bullish_engulfing,
            'bearish_engulfing': self._bearish_engulfing,
            'pin_bar': self._pin_bar,
            'doji': self._doji,
            'inside_bar': self._inside_bar,
            'marubozu': self._marubozu
        }

    def analyze(self, candles):
        """Analyze price action and return signal with confidence"""
        if len(candles) < 20:
            return {"signal": "NEUTRAL", "confidence": 0, "reason": "Insufficient data"}

        df = pd.DataFrame(candles)

        # Calculate basic features
        df['body_size'] = abs(df['close'] - df['open'])
        df['upper_shadow'] = df['high'] - df[['open', 'close']].max(axis=1)
        df['lower_shadow'] = df[['open', 'close']].min(axis=1) - df['low']
        df['total_range'] = df['high'] - df['low']
        df['body_ratio'] = df['body_size'] / (df['total_range'] + 1e-8)

        # Trend detection
        trend = self._detect_trend(df)

        # Support/Resistance levels
        support, resistance = self._find_support_resistance(df)

        # Pattern recognition
        patterns_found = self._detect_patterns(df)

        # Generate signal
        signal_data = self._generate_signal(df, trend, support, resistance, patterns_found)

        return signal_data

    def _detect_trend(self, df, period=10):
        """Detect current trend direction"""
        if len(df) < period:
            return "NEUTRAL"

        recent_closes = df['close'].tail(period)
        trend_slope = np.polyfit(range(len(recent_closes)), recent_closes, 1)[0]

        if trend_slope > 0.0001:
            return "BULLISH"
        elif trend_slope < -0.0001:
            return "BEARISH"
        else:
            return "NEUTRAL"

    def _find_support_resistance(self, df, window=20):
        """Find support and resistance levels"""
        if len(df) < window:
            return df['low'].min(), df['high'].max()

        recent_data = df.tail(window)
        support = recent_data['low'].min()
        resistance = recent_data['high'].max()

        return support, resistance

    def _detect_patterns(self, df):
        """Detect candlestick patterns"""
        patterns_found = []

        if len(df) < 2:
            return patterns_found

        current = df.iloc[-1]
        previous = df.iloc[-2]

        # Check each pattern
        for pattern_name, pattern_func in self.patterns.items():
            if pattern_func(previous, current):
                patterns_found.append(pattern_name)

        return patterns_found

    def _bullish_engulfing(self, prev, curr):
        """Bullish engulfing pattern"""
        return (prev['close'] < prev['open'] and  # Previous bearish
                curr['close'] > curr['open'] and  # Current bullish
                curr['open'] < prev['close'] and  # Current opens below prev close
                curr['close'] > prev['open'])     # Current closes above prev open

    def _bearish_engulfing(self, prev, curr):
        """Bearish engulfing pattern"""
        return (prev['close'] > prev['open'] and  # Previous bullish
                curr['close'] < curr['open'] and  # Current bearish
                curr['open'] > prev['close'] and  # Current opens above prev close
                curr['close'] < prev['open'])     # Current closes below prev open

    def _pin_bar(self, prev, curr):
        """Pin bar pattern"""
        body_size = abs(curr['close'] - curr['open'])
        total_range = curr['high'] - curr['low']
        upper_shadow = curr['high'] - max(curr['open'], curr['close'])
        lower_shadow = min(curr['open'], curr['close']) - curr['low']

        # Pin bar criteria
        return (body_size < total_range * 0.3 and  # Small body
                (upper_shadow > body_size * 2 or lower_shadow > body_size * 2))  # Long shadow

    def _doji(self, prev, curr):
        """Doji pattern"""
        body_size = abs(curr['close'] - curr['open'])
        total_range = curr['high'] - curr['low']

        return body_size < total_range * 0.1  # Very small body

    def _inside_bar(self, prev, curr):
        """Inside bar pattern"""
        return (curr['high'] < prev['high'] and curr['low'] > prev['low'])

    def _marubozu(self, prev, curr):
        """Marubozu pattern"""
        body_size = abs(curr['close'] - curr['open'])
        total_range = curr['high'] - curr['low']
        upper_shadow = curr['high'] - max(curr['open'], curr['close'])
        lower_shadow = min(curr['open'], curr['close']) - curr['low']

        # Marubozu criteria: large body with minimal shadows
        return (body_size > total_range * 0.8 and
                upper_shadow < total_range * 0.1 and
                lower_shadow < total_range * 0.1)

    def _generate_signal(self, df, trend, support, resistance, patterns):
        """Generate trading signal based on analysis"""
        current_price = df['close'].iloc[-1]
        confidence = 0
        signal = "NEUTRAL"
        reasons = []

        # Trend-based signals
        if trend == "BULLISH":
            confidence += 20
            reasons.append("Bullish trend")
        elif trend == "BEARISH":
            confidence += 20
            reasons.append("Bearish trend")

        # Support/Resistance signals
        price_range = resistance - support
        if price_range > 0:
            distance_to_support = (current_price - support) / price_range
            distance_to_resistance = (resistance - current_price) / price_range

            if distance_to_support < 0.1:  # Near support
                confidence += 15
                reasons.append("Near support level")
                if trend != "BEARISH":
                    signal = "UP"
            elif distance_to_resistance < 0.1:  # Near resistance
                confidence += 15
                reasons.append("Near resistance level")
                if trend != "BULLISH":
                    signal = "DOWN"

        # Pattern-based signals
        bullish_patterns = ['bullish_engulfing', 'pin_bar', 'marubozu']
        bearish_patterns = ['bearish_engulfing', 'pin_bar', 'marubozu']

        for pattern in patterns:
            if pattern in bullish_patterns:
                confidence += 25
                reasons.append(f"Bullish {pattern}")
                if signal != "DOWN":
                    signal = "UP"
            elif pattern in bearish_patterns:
                confidence += 25
                reasons.append(f"Bearish {pattern}")
                if signal != "UP":
                    signal = "DOWN"

        # Ensure confidence doesn't exceed 100
        confidence = min(confidence, 100)

        return {
            "signal": signal,
            "confidence": confidence,
            "reason": "; ".join(reasons) if reasons else "No clear signal",
            "trend": trend,
            "support": support,
            "resistance": resistance,
            "patterns": patterns
        }

# ============================================================================
# 🤖 ML MODEL PREDICTOR
# ============================================================================

class MLModelPredictor:
    """ML model for price prediction"""

    def __init__(self):
        self.model = None
        self.scaler = None
        self.feature_columns = None
        self.sequence_length = CONFIG["sequence_length"]

    def load_model(self):
        """Load trained model and metadata"""
        try:
            model_path = CONFIG["model_path"]
            if not os.path.exists(model_path):
                print(f"❌ Model not found: {model_path}")
                return False

            # Load model
            self.model = tf.keras.models.load_model(model_path)
            print(f"✅ Model loaded from {model_path}")

            # Try to load metadata
            metadata_path = model_path.replace('.h5', '_metadata.pkl')
            if os.path.exists(metadata_path):
                with open(metadata_path, 'rb') as f:
                    metadata = pickle.load(f)
                    self.scaler = metadata['scaler']
                    self.feature_columns = metadata['feature_columns']
                    self.sequence_length = metadata['sequence_length']
                print("✅ Model metadata loaded")
            else:
                print("⚠️ No metadata found, using default scaler")
                self.scaler = MinMaxScaler()

            return True

        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return False

    def predict(self, candles):
        """Predict using ML model"""
        if self.model is None:
            return {"signal": "NEUTRAL", "confidence": 0, "reason": "Model not loaded"}

        try:
            # Convert to DataFrame and engineer features
            df = pd.DataFrame(candles)
            df = self._engineer_features(df)

            if len(df) < self.sequence_length:
                return {"signal": "NEUTRAL", "confidence": 0, "reason": "Insufficient data"}

            # Prepare features
            if self.feature_columns:
                features = df[self.feature_columns].values
            else:
                # Use basic OHLC features if no metadata
                feature_cols = ['open', 'high', 'low', 'close']
                features = df[feature_cols].values

            # Scale features
            features_scaled = self.scaler.transform(features)

            # Create sequence
            sequence = features_scaled[-self.sequence_length:].reshape(1, self.sequence_length, -1)

            # Predict
            prediction = self.model.predict(sequence, verbose=0)[0]

            # Convert to signal
            predicted_class = np.argmax(prediction)
            confidence = float(np.max(prediction) * 100)

            signal_map = {0: "NEUTRAL", 1: "UP", 2: "DOWN"}
            signal = signal_map.get(predicted_class, "NEUTRAL")

            return {
                "signal": signal,
                "confidence": confidence,
                "reason": f"ML prediction (class {predicted_class})",
                "probabilities": prediction.tolist()
            }

        except Exception as e:
            print(f"❌ ML prediction error: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "reason": f"Prediction error: {e}"}

    def _engineer_features(self, df):
        """Engineer features for ML model"""
        # Basic OHLC features
        df['body_size'] = abs(df['close'] - df['open'])
        df['upper_shadow'] = df['high'] - df[['open', 'close']].max(axis=1)
        df['lower_shadow'] = df[['open', 'close']].min(axis=1) - df['low']
        df['total_range'] = df['high'] - df['low']
        df['body_to_range'] = df['body_size'] / (df['total_range'] + 1e-8)

        # Price changes
        df['price_change'] = df['close'].pct_change()
        df['high_change'] = df['high'].pct_change()
        df['low_change'] = df['low'].pct_change()

        # Fill NaN values
        df = df.bfill().fillna(0)

        return df

# ============================================================================
# 🎯 TRADING ENGINE
# ============================================================================

class TradingEngine:
    """Core trading engine combining price action and ML signals"""

    def __init__(self):
        self.client = None
        self.price_action = PriceActionAnalyzer()
        self.ml_model = MLModelPredictor()
        self.is_connected = False
        self.is_trading = False

        # Trading statistics
        self.stats = {
            "total_trades": 0,
            "wins": 0,
            "losses": 0,
            "win_rate": 0.0,
            "profit": 0.0,
            "daily_trades": 0,
            "consecutive_losses": 0,
            "last_trade_time": None
        }

        # Active trades
        self.active_trades = {}

    async def connect(self):
        """Connect to Quotex API"""
        try:
            print("🔌 Connecting to Quotex...")

            self.client = Quotex(
                email=CONFIG["email"],
                password=CONFIG["password"],
                lang="en"
            )

            check, reason = await self.client.connect()
            if check:
                print("✅ Connected successfully!")
                self.is_connected = True
                return True
            else:
                print(f"❌ Connection failed: {reason}")
                return False

        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False

    def load_models(self):
        """Load ML model"""
        return self.ml_model.load_model()

    async def get_balance(self):
        """Get current account balance"""
        if not self.is_connected:
            return 0.0
        try:
            balance = await self.client.get_balance()
            return balance
        except:
            return 0.0

    async def get_recent_candles(self, asset, period, count=100):
        """Get recent candles for analysis"""
        try:
            end_time = int(time.time())
            offset = period * count

            candles = await self.client.get_candles(
                asset=asset,
                end_from_time=end_time,
                offset=offset,
                period=period
            )

            return candles[-count:] if candles else []

        except Exception as e:
            print(f"❌ Error fetching candles: {e}")
            return []

    async def analyze_market(self, asset, timeframe):
        """Analyze market using both price action and ML"""
        candles = await self.get_recent_candles(asset, timeframe)

        if not candles:
            return {"signal": "NEUTRAL", "confidence": 0, "reason": "No data"}

        # Get price action analysis
        pa_analysis = self.price_action.analyze(candles)

        # Get ML prediction
        ml_analysis = self.ml_model.predict(candles)

        # Combine signals
        combined_signal = self._combine_signals(pa_analysis, ml_analysis)

        return combined_signal

    def _combine_signals(self, pa_signal, ml_signal):
        """Combine price action and ML signals"""
        # Weight the signals (60% price action, 40% ML)
        pa_weight = 0.6
        ml_weight = 0.4

        # Calculate combined confidence
        combined_confidence = (pa_signal["confidence"] * pa_weight +
                             ml_signal["confidence"] * ml_weight)

        # Determine final signal
        if pa_signal["signal"] == ml_signal["signal"] and pa_signal["signal"] != "NEUTRAL":
            # Both agree on direction
            final_signal = pa_signal["signal"]
            combined_confidence *= 1.2  # Boost confidence when both agree
        elif pa_signal["confidence"] > ml_signal["confidence"]:
            # Price action has higher confidence
            final_signal = pa_signal["signal"]
        else:
            # ML has higher confidence
            final_signal = ml_signal["signal"]

        # Cap confidence at 100
        combined_confidence = min(combined_confidence, 100)

        return {
            "signal": final_signal,
            "confidence": combined_confidence,
            "price_action": pa_signal,
            "ml_prediction": ml_signal,
            "reason": f"PA: {pa_signal['reason']} | ML: {ml_signal['reason']}"
        }

    async def execute_trade(self, asset, direction, amount, timeframe):
        """Execute a trade"""
        try:
            if not self.is_connected:
                return False, "Not connected"

            # Check risk management
            if not self._check_risk_management():
                return False, "Risk management limits exceeded"

            # Place trade
            trade_result = await self.client.buy(
                amount=amount,
                asset=asset,
                direction=direction.lower(),
                duration=timeframe
            )

            if trade_result[0]:
                trade_id = trade_result[1]

                # Store active trade
                self.active_trades[trade_id] = {
                    "asset": asset,
                    "direction": direction,
                    "amount": amount,
                    "timeframe": timeframe,
                    "start_time": datetime.now(),
                    "status": "active"
                }

                # Update stats
                self.stats["total_trades"] += 1
                self.stats["daily_trades"] += 1
                self.stats["last_trade_time"] = datetime.now()

                print(f"✅ Trade executed: {direction} {asset} ${amount} {timeframe}s")
                return True, trade_id
            else:
                print(f"❌ Trade failed: {trade_result[1]}")
                return False, trade_result[1]

        except Exception as e:
            print(f"❌ Trade execution error: {e}")
            return False, str(e)

    def _check_risk_management(self):
        """Check if trade passes risk management rules"""
        # Check daily loss limit
        if self.stats["profit"] <= -CONFIG["risk_management"]["max_daily_loss"]:
            return False

        # Check consecutive losses
        if self.stats["consecutive_losses"] >= CONFIG["risk_management"]["max_consecutive_losses"]:
            return False

        # Check daily trade limit
        if self.stats["daily_trades"] >= CONFIG["max_trades_per_hour"]:
            return False

        return True

    async def check_trade_results(self):
        """Check results of active trades"""
        for trade_id, trade_info in list(self.active_trades.items()):
            if trade_info["status"] == "active":
                # Check if trade should be finished
                elapsed = datetime.now() - trade_info["start_time"]
                if elapsed.total_seconds() >= trade_info["timeframe"] + 10:  # Add 10s buffer

                    try:
                        result, trade_data = await self.client.get_result(trade_id)

                        if result:
                            profit = float(trade_data.get("profitAmount", 0))

                            # Update trade status
                            trade_info["status"] = "win" if profit > 0 else "loss"
                            trade_info["profit"] = profit

                            # Update statistics
                            if profit > 0:
                                self.stats["wins"] += 1
                                self.stats["consecutive_losses"] = 0
                            else:
                                self.stats["losses"] += 1
                                self.stats["consecutive_losses"] += 1

                            self.stats["profit"] += profit
                            self.stats["win_rate"] = (self.stats["wins"] /
                                                    max(self.stats["total_trades"], 1)) * 100

                            print(f"📊 Trade result: {trade_info['status']} | Profit: ${profit}")

                    except Exception as e:
                        print(f"❌ Error checking trade result: {e}")

    def get_trading_stats(self):
        """Get current trading statistics"""
        return self.stats.copy()

    def get_active_trades(self):
        """Get list of active trades"""
        return [trade for trade in self.active_trades.values()
                if trade["status"] == "active"]

    def start_trading(self):
        """Start automated trading"""
        self.is_trading = True
        print("🚀 Trading started!")

    def stop_trading(self):
        """Stop automated trading"""
        self.is_trading = False
        print("⏹️ Trading stopped!")

    def close(self):
        """Close connection"""
        if self.client:
            self.client.close()
        self.is_connected = False

# ============================================================================
# 🎯 MAIN BOT CLASS
# ============================================================================

class QuotexTradingBot:
    """Main trading bot orchestrator"""

    def __init__(self):
        self.trading_engine = TradingEngine()
        self.is_running = False

    async def run_trading_loop(self):
        """Main trading loop"""
        print("🤖 Starting trading loop...")

        while self.is_running and self.trading_engine.is_trading:
            try:
                # Analyze market for each timeframe
                for timeframe in CONFIG["timeframes"]:
                    analysis = await self.trading_engine.analyze_market(
                        CONFIG["asset"], timeframe
                    )

                    print(f"📊 {CONFIG['asset']} {timeframe}s: {analysis['signal']} "
                          f"({analysis['confidence']:.1f}%)")

                    # Execute trade if confidence is high enough
                    if (analysis["confidence"] >= CONFIG["confidence_threshold"] and
                        analysis["signal"] in ["UP", "DOWN"]):

                        success, result = await self.trading_engine.execute_trade(
                            asset=CONFIG["asset"],
                            direction=analysis["signal"],
                            amount=CONFIG["trade_amount"],
                            timeframe=timeframe
                        )

                        if success:
                            print(f"✅ Trade placed: {analysis['signal']} {timeframe}s")
                        else:
                            print(f"❌ Trade failed: {result}")

                # Check trade results
                await self.trading_engine.check_trade_results()

                # Wait before next analysis
                await asyncio.sleep(30)  # Analyze every 30 seconds

            except Exception as e:
                print(f"❌ Trading loop error: {e}")
                await asyncio.sleep(60)  # Wait longer on error

    def start(self):
        """Start the bot"""
        self.is_running = True

    def stop(self):
        """Stop the bot"""
        self.is_running = False
        self.trading_engine.stop_trading()

# ============================================================================
# 🚀 MAIN EXECUTION
# ============================================================================

def main():
    """Main function to start the bot with GUI"""
    print("🤖 QUOTEX AI TRADING BOT")
    print("=" * 50)

    # Create bot instance
    bot = QuotexTradingBot()

    # Import and create GUI
    from trading_gui import TradingDashboard

    # Create GUI with trading engine
    dashboard = TradingDashboard(bot.trading_engine)

    # Start bot
    bot.start()

    # Start trading loop in background
    def start_trading_loop():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(bot.run_trading_loop())

    trading_thread = threading.Thread(target=start_trading_loop, daemon=True)
    trading_thread.start()

    # Run GUI (blocking)
    try:
        dashboard.run()
    except KeyboardInterrupt:
        print("\n⏹️ Shutting down...")
    finally:
        bot.stop()
        bot.trading_engine.close()

if __name__ == "__main__":
    main()